import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Chemical from '@/models/Chemical';
import Regulation from '@/models/Regulation';
import * as XLSX from 'xlsx';

// For debugging
console.log('Import API: Models loaded', {
  Chemical: !!Chemical,
  Regulation: !!Regulation
});

// Add runtime directive for Node.js
export const runtime = 'nodejs';

// Maximum file size (5MB)
const MAX_FILE_SIZE = 5 * 1024 * 1024;

export async function POST(request: NextRequest) {
  try {
    // Check if the request is multipart/form-data
    const contentType = request.headers.get('content-type') || '';
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json(
        { success: false, error: 'Request must be multipart/form-data' },
        { status: 400 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Parse the form data
    const formData = await request.formData();
    const file = formData.get('file') as File | null;
    const regulationNameRaw = formData.get('regulationName');
    const regulationName = regulationNameRaw ? String(regulationNameRaw) : '';
    const regulationIdRaw = formData.get('regulationId');
    const regulationId = regulationIdRaw ? String(regulationIdRaw) : '';
    const categoryRaw = formData.get('category');
    const category = categoryRaw ? String(categoryRaw) : 'Imported';
    const regionRaw = formData.get('region');
    const region = regionRaw ? String(regionRaw) : 'Global';

    console.log('Import request parameters:', {
      regulationName,
      regulationId,
      category,
      region,
      fileName: file?.name
    });

    // Check if a file was provided
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { success: false, error: `File size exceeds the limit of ${MAX_FILE_SIZE / (1024 * 1024)}MB` },
        { status: 400 }
      );
    }

    // Get file details
    const fileName = file.name;
    const fileType = file.type;

    // Check file type
    if (!fileType.includes('spreadsheet') && !fileType.includes('csv') &&
        !fileName.endsWith('.xlsx') && !fileName.endsWith('.xls') && !fileName.endsWith('.csv')) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only Excel and CSV files are supported.' },
        { status: 400 }
      );
    }

    // Convert the file to an array buffer
    const fileBuffer = await file.arrayBuffer();

    // Parse the Excel/CSV file
    const workbook = XLSX.read(fileBuffer, { type: 'array' });
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    if (data.length === 0) {
      return NextResponse.json(
        { success: false, error: 'File contains no data' },
        { status: 400 }
      );
    }

    // Process the data
    const results = {
      added: 0,
      updated: 0,
      skipped: 0,
      errors: [] as string[],
      chemicals: [] as any[]
    };

    // Create or find regulation if regulationName or regulationId is provided
    let regulation = null;

    if (regulationId) {
      console.log(`Looking for regulation with ID: ${regulationId}`);
      regulation = await Regulation.findById(regulationId);
      if (!regulation) {
        return NextResponse.json(
          { success: false, error: 'Regulation not found' },
          { status: 404 }
        );
      }
      console.log(`Found regulation: ${regulation.name}`);
    } else if (regulationName && regulationName.trim() !== '') {
      console.log(`Looking for regulation with name: ${regulationName}`);
      // Try to find an existing regulation with this name
      regulation = await Regulation.findOne({
        name: { $regex: new RegExp('^' + regulationName.trim() + '$', 'i') }
      });

      if (regulation) {
        console.log(`Found existing regulation: ${regulation.name}`);
      } else {
        // Create a new regulation with the provided name
        console.log(`Creating new regulation: ${regulationName}`);
        regulation = await Regulation.create({
          name: regulationName.trim(),
          country: 'Unknown',
          region: region,
          description: `Imported from ${fileName}`,
          categories: [category],
          lastUpdated: new Date()
        });
        console.log(`Created new regulation with ID: ${regulation._id}`);
      }
    } else {
      console.log('No regulation ID or name provided');
    }

    // Process each row in the file
    for (const row of data) {
      try {
        // Find chemical name column (could be "chemical name", "substance name", etc.)
        const chemicalNameKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('chemical') && key.toLowerCase().includes('name') ||
          key.toLowerCase().includes('substance') && key.toLowerCase().includes('name') ||
          key.toLowerCase() === 'name'
        );

        // Find CAS number column
        const casNumberKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('cas') ||
          key.toLowerCase().includes('cas no') ||
          key.toLowerCase().includes('cas number')
        );

        // Find EC number column
        const ecNumberKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('ec') && key.toLowerCase().includes('number') ||
          key.toLowerCase().includes('ec no') ||
          key.toLowerCase().includes('ec-number') ||
          key.toLowerCase().includes('einecs') ||
          key.toLowerCase() === 'ec'
        );

        // Find SML value column
        const smlKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('sml') ||
          key.toLowerCase().includes('specific migration') ||
          key.toLowerCase().includes('migration limit')
        );

        // Find notes column
        const notesKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('note') ||
          key.toLowerCase().includes('comment') ||
          key.toLowerCase().includes('remark')
        );

        // Find restrictions column
        const restrictionsKeys = Object.keys(row).filter(key =>
          key.toLowerCase().includes('restriction') ||
          key.toLowerCase().includes('limitation') ||
          key.toLowerCase().includes('condition')
        );

        // Extract values
        const chemicalName = chemicalNameKeys.length > 0 ? row[chemicalNameKeys[0]] : null;
        const casNumber = casNumberKeys.length > 0 ? row[casNumberKeys[0]] : null;
        const ecNumber = ecNumberKeys.length > 0 ? row[ecNumberKeys[0]] : null;
        let smlValue = smlKeys.length > 0 ? row[smlKeys[0]] : null;
        const notes = notesKeys.length > 0 ? row[notesKeys[0]] : '';
        const restrictions = restrictionsKeys.length > 0 ? row[restrictionsKeys[0]] : '';

        // Extract SML value and unit
        let smlUnit = 'mg/kg'; // Default unit
        if (smlValue && typeof smlValue === 'string') {
          // Extract numeric part from SML value (e.g., "0.05 mg/kg" -> "0.05")
          const numericMatch = smlValue.match(/[\d.]+/);
          if (numericMatch) {
            // Try to extract the unit part
            const unitMatch = smlValue.match(/[a-zA-Z\/]+/g);
            if (unitMatch && unitMatch.length > 0) {
              smlUnit = unitMatch.join('').trim();
            }
            smlValue = numericMatch[0];
          }
        }

        // Create structured SML(T) Group data
        const smlGroupData: Record<string, any> = {};
        if (smlGroupRestrictionNo) smlGroupData['restriction_no'] = smlGroupRestrictionNo;
        if (smlGroupFCMSubstances) smlGroupData['fcm_substances'] = smlGroupFCMSubstances;
        if (smlGroupValue) smlGroupData['group_sml_value'] = smlGroupValue;
        if (verificationNotes) smlGroupData['verification_notes'] = verificationNotes;

        // Create a comprehensive data structure for all imported columns
        const processedColumns = new Set([
          ...chemicalNameKeys,
          ...casNumberKeys,
          ...ecNumberKeys,
          ...smlKeys,
          ...smlGroupRestrictionKeys,
          ...smlGroupFCMKeys,
          ...smlGroupValueKeys,
          ...notesKeys,
          ...verificationNotesKeys,
          ...restrictionsKeys
        ]);

        // Collect ALL additional information from other columns
        const additionalInfo: Record<string, any> = {};
        const allColumnData: Record<string, any> = {};

        // Store ALL columns with their original names and values
        Object.keys(row).forEach(key => {
          const value = row[key];
          allColumnData[key] = value;

          // Only put unprocessed columns in additionalInfo
          if (!processedColumns.has(key)) {
            additionalInfo[key] = value;
          }
        });

        // Skip row if neither chemical name nor CAS number is provided
        if (!chemicalName && !casNumber) {
          results.skipped++;
          continue;
        }

        // Try to find existing chemical by CAS number first, then by name
        let existingChemical = null;
        if (casNumber) {
          existingChemical = await Chemical.findOne({ cas_number: casNumber });
        }

        if (!existingChemical && chemicalName) {
          existingChemical = await Chemical.findOne({ chemical_name: chemicalName });
        }

        if (existingChemical) {
          // Update existing chemical with new EC number if provided and not already set
          let chemicalUpdated = false;
          if (ecNumber && !existingChemical.ec_number) {
            existingChemical.ec_number = ecNumber;
            chemicalUpdated = true;
          }

          if (chemicalUpdated) {
            await existingChemical.save();
            console.log(`Updated existing chemical ${existingChemical.chemical_name} with EC number: ${ecNumber}`);
          }

          results.updated++;
          results.chemicals.push({
            _id: existingChemical._id,
            chemical_name: existingChemical.chemical_name,
            cas_number: existingChemical.cas_number,
            ec_number: existingChemical.ec_number,
            updated: true
          });

          // If regulation is provided, link it to the chemical using embedded schema
          if (regulation) {
            console.log(`Linking existing chemical ${existingChemical.chemical_name} to regulation ${regulation.name}`);

            try {
              // Check if the chemical-regulation relationship already exists in the embedded regulations array
              const existingRegulationIndex = existingChemical.regulations.findIndex(
                reg => reg.regulation_id.toString() === regulation._id.toString()
              );

              if (existingRegulationIndex === -1) {
                // Create new embedded regulation entry
                console.log(`Creating new embedded regulation relationship with SML value: ${smlValue || 'none'}`);
                console.log(`SML(T) Group data:`, smlGroupData);
                console.log(`All column data captured:`, Object.keys(allColumnData));

                const regulationEntry = {
                  regulation_id: regulation._id,
                  regulation_name: regulation.name,
                  regulation_type: category,
                  list_type: regulation.list_type || 'unknown',
                  country: regulation.country,
                  region: regulation.region,
                  limits: smlValue ? {
                    sml_value: smlValue,
                    sml_unit: smlUnit || 'mg/kg'
                  } : undefined,
                  specific_data: {
                    // Store all additional unprocessed columns
                    additionalInfo,
                    // Store SML(T) Group specific data
                    smlGroupData: Object.keys(smlGroupData).length > 0 ? smlGroupData : undefined,
                    // Store ALL original column data for complete audit trail
                    originalRowData: allColumnData,
                    // Store column mapping for reference
                    columnMapping: {
                      chemical_name: chemicalNameKeys,
                      cas_number: casNumberKeys,
                      ec_number: ecNumberKeys,
                      sml_keys: smlKeys,
                      sml_group_restriction: smlGroupRestrictionKeys,
                      sml_group_fcm: smlGroupFCMKeys,
                      sml_group_value: smlGroupValueKeys,
                      notes: notesKeys,
                      verification_notes: verificationNotesKeys,
                      restrictions: restrictionsKeys
                    }
                  },
                  source_info: {
                    file_name: file?.name || 'import',
                    file_type: 'excel_import',
                    imported_date: new Date(),
                    original_data: allColumnData
                  },
                  notes: notes || '',
                  restrictions: restrictions || ''
                };

                // Update the chemical with the embedded regulation
                const updateResult = await Chemical.findByIdAndUpdate(
                  existingChemical._id,
                  { $addToSet: { regulations: regulationEntry } },
                  { new: true }
                );

                console.log(`Added embedded regulation to existing chemical`);

                // Log the updated chemical for debugging
                console.log(`Updated existing chemical regulations array:`, {
                  chemicalId: existingChemical._id,
                  chemicalName: existingChemical.chemical_name,
                  regulationId: regulation._id,
                  regulationName: regulation.name,
                  regulationsCount: updateResult?.regulations?.length || 0
                });
              } else {
                // Update the existing embedded regulation with any new information
                console.log(`Updating existing embedded regulation`);
                let updated = false;
                const existingReg = existingChemical.regulations[existingRegulationIndex];

                if (smlValue && existingReg.limits?.sml_value !== smlValue) {
                  console.log(`Updating existing regulation SML value from ${existingReg.limits?.sml_value} to ${smlValue}`);
                  if (!existingReg.limits) existingReg.limits = {};
                  existingReg.limits.sml_value = smlValue;
                  existingReg.limits.sml_unit = smlUnit || 'mg/kg';
                  updated = true;
                }

                if (notes && existingReg.notes !== notes) {
                  console.log(`Updating existing regulation notes`);
                  existingReg.notes = notes;
                  updated = true;
                }

                if (restrictions && existingReg.restrictions !== restrictions) {
                  console.log(`Updating existing regulation restrictions`);
                  existingReg.restrictions = restrictions;
                  updated = true;
                }

                // Merge additional info and SML(T) Group data
                if (Object.keys(additionalInfo).length > 0 || Object.keys(smlGroupData).length > 0) {
                  console.log(`Updating additional information and SML(T) Group data`);
                  existingReg.specific_data = {
                    ...existingReg.specific_data,
                    additionalInfo,
                    smlGroupData: Object.keys(smlGroupData).length > 0 ? smlGroupData : existingReg.specific_data?.smlGroupData,
                    originalRowData: allColumnData,
                    columnMapping: {
                      chemical_name: chemicalNameKeys,
                      cas_number: casNumberKeys,
                      ec_number: ecNumberKeys,
                      sml_keys: smlKeys,
                      sml_group_restriction: smlGroupRestrictionKeys,
                      sml_group_fcm: smlGroupFCMKeys,
                      sml_group_value: smlGroupValueKeys,
                      notes: notesKeys,
                      verification_notes: verificationNotesKeys,
                      restrictions: restrictionsKeys
                    }
                  };
                  updated = true;
                }

                if (updated) {
                  existingReg.updated_date = new Date();
                  await existingChemical.save();
                  console.log(`Updated embedded regulation for existing chemical`);
                } else {
                  console.log(`Embedded regulation already exists with current data`);
                }
              }
            } catch (relationError) {
              console.error(`Error linking chemical to regulation:`, relationError);
              results.errors.push(`Error linking chemical ${existingChemical.chemical_name} to regulation ${regulation.name}: ${relationError.message}`);
            }
          }
        } else {
          // Create new chemical using the new schema
          const newChemical = await Chemical.create({
            chemical_name: chemicalName || `Unknown (CAS: ${casNumber})`,
            cas_number: casNumber || 'Unknown',
            ec_number: ecNumber || undefined,
            alternative_names: [],
            additional_data: Object.keys(allColumnData).length > 0 ? {
              import_source: allColumnData,
              sml_group_data: Object.keys(smlGroupData).length > 0 ? smlGroupData : undefined
            } : undefined,
            entry_method: 'import',
            regulations: [],
            source_files: []
          });

          results.added++;
          results.chemicals.push({
            _id: newChemical._id,
            chemical_name: newChemical.chemical_name,
            cas_number: newChemical.cas_number,
            added: true
          });

          // If regulation is provided, link it to the chemical using embedded schema
          if (regulation) {
            console.log(`Linking new chemical ${newChemical.chemical_name} to regulation ${regulation.name}`);

            try {
              // Create embedded regulation entry
              console.log(`Creating regulation entry for new chemical with SML(T) Group data:`, smlGroupData);

              const regulationEntry = {
                regulation_id: regulation._id,
                regulation_name: regulation.name,
                regulation_type: category,
                list_type: regulation.list_type || 'unknown',
                country: regulation.country,
                region: regulation.region,
                limits: smlValue ? {
                  sml_value: smlValue,
                  sml_unit: smlUnit || 'mg/kg'
                } : undefined,
                specific_data: {
                  // Store all additional unprocessed columns
                  additionalInfo,
                  // Store SML(T) Group specific data
                  smlGroupData: Object.keys(smlGroupData).length > 0 ? smlGroupData : undefined,
                  // Store ALL original column data for complete audit trail
                  originalRowData: allColumnData,
                  // Store column mapping for reference
                  columnMapping: {
                    chemical_name: chemicalNameKeys,
                    cas_number: casNumberKeys,
                    ec_number: ecNumberKeys,
                    sml_keys: smlKeys,
                    sml_group_restriction: smlGroupRestrictionKeys,
                    sml_group_fcm: smlGroupFCMKeys,
                    sml_group_value: smlGroupValueKeys,
                    notes: notesKeys,
                    verification_notes: verificationNotesKeys,
                    restrictions: restrictionsKeys
                  }
                },
                source_info: {
                  file_name: file?.name || 'import',
                  file_type: 'excel_import',
                  imported_date: new Date(),
                  original_data: allColumnData
                },
                notes: notes || '',
                restrictions: restrictions || ''
              };

              // Update the chemical with the embedded regulation
              const updateResult = await Chemical.findByIdAndUpdate(
                newChemical._id,
                { $addToSet: { regulations: regulationEntry } },
                { new: true }
              );

              console.log(`Added embedded regulation to new chemical`);

              // Log the updated chemical for debugging
              console.log(`Updated new chemical regulations array:`, {
                chemicalId: newChemical._id,
                chemicalName: newChemical.chemical_name,
                regulationId: regulation._id,
                regulationName: regulation.name,
                regulationsCount: updateResult?.regulations?.length || 0
              });
            } catch (relationError) {
              console.error(`Error linking new chemical to regulation:`, relationError);
              results.errors.push(`Error linking chemical ${newChemical.chemical_name} to regulation ${regulation.name}: ${relationError.message}`);
            }
          }
        }
      } catch (rowError) {
        console.error('Error processing row:', rowError, row);
        results.errors.push(`Error processing row: ${rowError instanceof Error ? rowError.message : 'Unknown error'}`);
        results.skipped++;
      }
    }

    // Prepare the response
    const response = {
      success: true,
      data: {
        ...results,
        regulation: regulation ? {
          _id: regulation._id,
          name: regulation.name
        } : null,
        // Include information about captured columns
        columnInfo: {
          totalColumns: allHeaders.length,
          allHeaders: allHeaders,
          message: `Successfully captured all ${allHeaders.length} columns from the imported file`
        }
      },
      message: `Processed ${data.length} rows: ${results.added} added, ${results.updated} updated, ${results.skipped} skipped. Captured ${allHeaders.length} columns including SML(T) Group data.`
    };

    if (regulation) {
      response.message += ` (Regulation: ${regulation.name})`;
    }

    console.log(`Import completed. Total columns captured: ${allHeaders.length}`);
    console.log(`Column headers: ${allHeaders.join(', ')}`);

    return NextResponse.json(response);
  } catch (error) {
    console.error('Error importing chemicals:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json(
      { success: false, error: errorMessage },
      { status: 500 }
    );
  }
}

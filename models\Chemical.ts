import mongoose, { Schema, Document, Model } from 'mongoose';

// Define TypeScript interfaces for the new schema
export type ListType = 'positive' | 'negative' | 'mixed' | 'unknown';

export interface IRegulationLimits {
  sml_value?: string;
  sml_unit?: string;
  max_level_percent?: number;
}

export interface ISourceInfo {
  file_name: string;
  file_type: string;
  imported_date: Date;
  row_number?: number;
  original_data?: Record<string, any>;
}

export interface IChemicalRegulation {
  regulation_id: mongoose.Types.ObjectId;
  regulation_name: string;
  regulation_type: string;
  list_type: ListType;
  country: string;
  region: string;
  limits?: IRegulationLimits;
  specific_data?: Record<string, any>;
  source_info: ISourceInfo;
  notes?: string;
  restrictions?: string;
  added_date: Date;
  updated_date: Date;
}

export interface IChemical extends Document {
  chemical_name: string;
  cas_number: string;
  alternative_names: string[];
  ec_number?: string;
  regulations: IChemicalRegulation[];
  source_files: string[];
  // New field to capture ALL additional data when adding chemicals
  additional_data?: Record<string, any>;
  // Track how the chemical was added (manual, import, etc.)
  entry_method?: string;
  created_at: Date;
  updated_at: Date;
  uploadResults: mongoose.Types.ObjectId[];
  calculationResults: mongoose.Types.ObjectId[];
}

// Define the embedded regulation schema
const RegulationLimitsSchema = new Schema({
  sml_value: { type: String },
  sml_unit: { type: String },
  max_level_percent: { type: Number }
}, { _id: false });

const SourceInfoSchema = new Schema({
  file_name: { type: String, required: true },
  file_type: { type: String, required: true },
  imported_date: { type: Date, required: true },
  row_number: { type: Number },
  original_data: { type: Schema.Types.Mixed }
}, { _id: false });

const ChemicalRegulationSchema = new Schema({
  regulation_id: { type: Schema.Types.ObjectId, required: true },
  regulation_name: { type: String, required: true },
  regulation_type: { type: String, required: true },
  list_type: {
    type: String,
    enum: ['positive', 'negative', 'mixed', 'unknown'],
    required: true
  },
  country: { type: String, required: true },
  region: { type: String, required: true },
  limits: { type: RegulationLimitsSchema },
  specific_data: { type: Schema.Types.Mixed },
  source_info: { type: SourceInfoSchema, required: true },
  notes: { type: String },
  restrictions: { type: String },
  added_date: { type: Date, default: Date.now },
  updated_date: { type: Date, default: Date.now }
}, { _id: false });

const ChemicalSchema: Schema<IChemical> = new Schema(
  {
    chemical_name: { type: String, required: true },
    cas_number: { type: String, required: true, unique: true },
    alternative_names: [{ type: String }],
    ec_number: { type: String },
    regulations: [ChemicalRegulationSchema],
    source_files: [{ type: String }],
    // New fields for comprehensive data capture
    additional_data: { type: Schema.Types.Mixed },
    entry_method: {
      type: String,
      enum: ['manual', 'import', 'api', 'bulk'],
      default: 'manual'
    },
    uploadResults: [{ type: Schema.Types.ObjectId, ref: 'UploadResult' }],
    calculationResults: [{ type: Schema.Types.ObjectId, ref: 'CalculationResult' }],
  },
  {
    timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' },
  }
);

// Create or reuse the Chemical model
const Chemical: Model<IChemical> =
  mongoose.models.Chemical || mongoose.model<IChemical>('Chemical', ChemicalSchema);

export default Chemical;
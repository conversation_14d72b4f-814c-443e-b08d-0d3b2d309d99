import { NextRequest, NextResponse } from "next/server";
import dbConnect from "@/lib/dbConnect";
import Chemical from "@/models/Chemical";
import mongoose from 'mongoose';

// Add runtime directive for Node.js
export const runtime = 'nodejs';

interface Params {
  id: string;
}

// Helper function to validate ObjectId
function isValidObjectId(id: string): boolean {
  return mongoose.Types.ObjectId.isValid(id);
}

// GET handler for a single chemical
export async function GET(request: NextRequest, { params }: { params: Params }) {
  await dbConnect();
  // Get the ID from params
  const { id } = await params;

  if (!isValidObjectId(id)) {
    return NextResponse.json({ success: false, error: "Invalid chemical ID format" }, { status: 400 });
  }

  try {
    // Fetch the chemical with embedded regulations
    const chemical = await Chemical.findById(id).lean();

    if (!chemical) {
      return NextResponse.json({ success: false, error: "Chemical not found" }, { status: 404 });
    }

    console.log(`API V2: Found chemical with ${chemical.regulations?.length || 0} regulations`);

    return NextResponse.json({ success: true, data: chemical });
  } catch (error) {
    console.error("API V2 Error fetching chemical:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json({ success: false, error: errorMessage }, { status: 400 });
  }
}

// PUT handler to update a chemical
export async function PUT(request: NextRequest, { params }: { params: Params }) {
  await dbConnect();
  // Get the ID from params
  const { id } = await params;

  if (!isValidObjectId(id)) {
    return NextResponse.json({ success: false, error: "Invalid chemical ID format" }, { status: 400 });
  }

  try {
    const body = await request.json();
    const { chemical_name, cas_number, alternative_names, ec_number } = body;

    // Validate required fields
    if (!chemical_name || !cas_number) {
      return NextResponse.json(
        { success: false, error: "Chemical name and CAS number are required" },
        { status: 400 }
      );
    }

    // Check if another chemical with the same CAS number exists (excluding this one)
    const existingChemical = await Chemical.findOne({
      cas_number,
      _id: { $ne: id }
    });

    if (existingChemical) {
      return NextResponse.json(
        { success: false, error: "Another chemical with this CAS number already exists" },
        { status: 409 }
      );
    }

    // Update the chemical
    const updatedChemical = await Chemical.findByIdAndUpdate(
      id,
      {
        chemical_name,
        cas_number,
        alternative_names: alternative_names || [],
        ec_number: ec_number || undefined
      },
      { new: true, runValidators: true }
    );

    if (!updatedChemical) {
      return NextResponse.json({ success: false, error: "Chemical not found" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: updatedChemical,
      message: "Chemical updated successfully"
    });
  } catch (error) {
    console.error("API V2 Error updating chemical:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json({ success: false, error: errorMessage }, { status: 400 });
  }
}

const mongoose = require('mongoose');

// Connection string - update this to match your MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/compliance-track';

async function swapCollections() {
  try {
    console.log('🔄 Starting collection swap...');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;

    // Check if collections exist
    const collections = await db.listCollections().toArray();
    const collectionNames = collections.map(c => c.name);
    
    console.log('📋 Available collections:', collectionNames);

    if (!collectionNames.includes('chemicals_new')) {
      throw new Error('chemicals_new collection not found! Run migration first.');
    }

    if (!collectionNames.includes('chemicals')) {
      throw new Error('chemicals collection not found!');
    }

    // Step 1: Rename original chemicals to backup
    console.log('📦 Step 1: Backing up original chemicals collection...');
    await db.collection('chemicals').rename('chemicals_backup');
    console.log('✅ Renamed chemicals → chemicals_backup');

    // Step 2: Rename new chemicals to chemicals
    console.log('🔄 Step 2: Activating new chemicals collection...');
    await db.collection('chemicals_new').rename('chemicals');
    console.log('✅ Renamed chemicals_new → chemicals');

    // Verify the swap
    console.log('🔍 Verifying the swap...');
    const newChemicalsCount = await db.collection('chemicals').countDocuments();
    const backupChemicalsCount = await db.collection('chemicals_backup').countDocuments();
    
    console.log(`✅ New chemicals collection: ${newChemicalsCount} documents`);
    console.log(`✅ Backup chemicals collection: ${backupChemicalsCount} documents`);

    if (newChemicalsCount === backupChemicalsCount) {
      console.log('🎉 Collection swap completed successfully!');
      console.log('📝 The application is now using the new schema.');
      console.log('⚠️  Keep chemicals_backup for safety until you verify everything works.');
    } else {
      console.log('⚠️  Warning: Document counts don\'t match. Please verify manually.');
    }

  } catch (error) {
    console.error('💥 Collection swap failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the swap
if (require.main === module) {
  swapCollections();
}

module.exports = { swapCollections };

import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Chemical from '@/models/Chemical';
import Regulation from '@/models/Regulation';

// Make sure all models are imported before using them
import '@/models/Chemical';
import '@/models/Regulation';

export async function GET(request: NextRequest) {
  try {
    console.log('API Search V2: Starting search request');

    // Connect to the database
    console.log('API Search V2: Connecting to database');
    const mongoose = await dbConnect();
    console.log('API Search V2: Database connection established:',
      mongoose.connection.readyState === 1 ? 'Connected' : 'Not connected');

    // Get search parameters from the URL
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';
    const region = searchParams.get('region') || '';
    const categories = searchParams.getAll('category');

    console.log('API Search V2: Search parameters:', {
      query,
      region,
      categories: categories.join(', ')
    });

    // Build the search query
    let searchQuery: any = {};

    // Search by name or CAS number if query is provided
    if (query) {
      // Check if the query might be a CAS number (contains digits)
      if (/\d/.test(query)) {
        // For potential CAS numbers, create a version without hyphens for comparison
        const queryWithoutHyphens = query.replace(/-/g, '');

        // Create a regex pattern that will match the CAS number regardless of hyphens
        // This works by making each hyphen optional in the pattern
        const casRegexPattern = queryWithoutHyphens.split('').join('-?');

        console.log('API Search V2: Detected potential CAS number search');
        console.log('API Search V2: Original query:', query);
        console.log('API Search V2: Query without hyphens:', queryWithoutHyphens);
        console.log('API Search V2: CAS regex pattern:', casRegexPattern);

        searchQuery = {
          $or: [
            { chemical_name: { $regex: query, $options: 'i' } },
            // Use the special regex pattern for CAS numbers
            { cas_number: { $regex: casRegexPattern, $options: 'i' } },
            { alternative_names: { $in: [new RegExp(query, 'i')] } }
          ]
        };
      } else {
        // For non-CAS number queries (no digits), use standard regex search
        searchQuery = {
          $or: [
            { chemical_name: { $regex: query, $options: 'i' } },
            { cas_number: { $regex: query, $options: 'i' } },
            { alternative_names: { $in: [new RegExp(query, 'i')] } }
          ]
        };
      }

      console.log('API Search V2: Search query:', JSON.stringify(searchQuery));
    }

    console.log('API Search V2: Executing search query:', JSON.stringify(searchQuery));

    // Find chemicals matching the search criteria with embedded regulations
    let chemicals = await Chemical.find(searchQuery).lean();

    console.log(`API Search V2: Found ${chemicals.length} chemicals matching query`);

    // Filter by region if provided
    let filteredChemicals = chemicals;
    if (region && region !== 'all') {
      filteredChemicals = filteredChemicals.filter(chemical => {
        return chemical.regulations && chemical.regulations.some((reg: any) =>
          reg.region === region
        );
      });
    }

    // Filter by categories if provided
    if (categories && categories.length > 0) {
      // We need to populate regulation data to filter by categories
      // For now, we'll skip category filtering since it requires regulation lookup
      console.log('API Search V2: Category filtering not yet implemented for new schema');
    }

    // Format the response
    const formattedChemicals = filteredChemicals.map(chemical => {
      // Extract regulations info from embedded regulations array
      const regulations = (chemical.regulations || []).map((reg: any) => {
        return {
          id: reg.regulation_id?.toString() || '',
          name: reg.regulation_name || '',
          shortName: reg.regulation_name || '', // Use full name as shortName for now
          country: reg.country || '',
          region: reg.region || '',
          categories: [], // Categories would need to be looked up from Regulation collection
          smlValue: reg.limits?.sml_value || '',
          smlUnit: reg.limits?.sml_unit || 'mg/kg',
          notes: reg.notes || '',
          restrictions: reg.restrictions || '',
          additionalInfo: reg.specific_data || {},
          list_type: reg.list_type || 'unknown'
        };
      });

      // Convert chemical ID to string
      const chemicalId = chemical._id.toString();
      console.log(`API Search V2: Chemical ID for ${chemical.chemical_name}:`, chemicalId);

      // Return formatted chemical with new field names
      return {
        id: chemicalId,
        name: chemical.chemical_name,
        casNumber: chemical.cas_number,
        // Remove status and risk fields - they no longer exist
        regulations: regulations
      };
    });

    return NextResponse.json({
      success: true,
      data: formattedChemicals
    });
  } catch (error) {
    console.error('API Search V2: Error searching chemicals:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error
      ? error.message
      : 'Unknown error occurred';

    console.error('API Search V2: Error details:', {
      message: errorMessage,
      stack: error instanceof Error ? error.stack : 'No stack trace available'
    });

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to search chemicals',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

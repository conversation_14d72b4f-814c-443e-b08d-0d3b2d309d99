const mongoose = require('mongoose');

// Connection string - update this to match your MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/compliance-track';

async function checkDatabase() {
  try {
    console.log('🔍 Checking database structure...');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;

    // Check collections
    const collections = await db.listCollections().toArray();
    console.log('📋 Available collections:', collections.map(c => c.name));

    // Check a sample chemical document
    console.log('\n🧪 Sample chemical document:');
    const sampleChemical = await db.collection('chemicals').findOne({});
    console.log(JSON.stringify(sampleChemical, null, 2));

    // Check field names in chemicals collection
    console.log('\n📊 Field analysis:');
    const chemicals = await db.collection('chemicals').find({}).limit(5).toArray();
    
    chemicals.forEach((chem, index) => {
      console.log(`\nChemical ${index + 1}:`);
      console.log('- Fields:', Object.keys(chem));
      console.log('- name field:', chem.name);
      console.log('- chemical_name field:', chem.chemical_name);
      console.log('- casNumber field:', chem.casNumber);
      console.log('- cas_number field:', chem.cas_number);
      console.log('- regulations count:', chem.regulations ? chem.regulations.length : 0);
    });

    // Check backup collection too
    console.log('\n📦 Sample backup chemical document:');
    const backupChemical = await db.collection('chemicals_backup').findOne({});
    console.log('Backup fields:', Object.keys(backupChemical));

  } catch (error) {
    console.error('💥 Database check failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the check
if (require.main === module) {
  checkDatabase();
}

module.exports = { checkDatabase };

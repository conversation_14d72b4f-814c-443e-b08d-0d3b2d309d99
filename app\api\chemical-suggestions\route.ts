import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Chemical from '@/models/Chemical';

// Make sure all models are imported before using them
import '@/models/Chemical';

export async function GET(request: NextRequest) {
  try {
    console.log('API Suggestions: Starting request');

    // Connect to the database
    console.log('API Suggestions: Connecting to database');
    const mongoose = await dbConnect();
    console.log('API Suggestions: Database connection established:',
      mongoose.connection.readyState === 1 ? 'Connected' : 'Not connected');

    // Get search parameters from the URL
    const searchParams = request.nextUrl.searchParams;
    const query = searchParams.get('query') || '';

    if (!query || query.length < 1) {
      return NextResponse.json({
        success: true,
        data: []
      });
    }

    console.log('API Suggestions: Search query:', query);

    // Build the search query
    let searchQuery: any = {
      $or: []
    };

    // Always search by name
    searchQuery.$or.push({ name: { $regex: query, $options: 'i' } });

    // Check if the query might be a CAS number (contains digits)
    if (/\d/.test(query)) {
      // For potential CAS numbers, create a version without hyphens for comparison
      const queryWithoutHyphens = query.replace(/-/g, '');

      // Create a regex pattern that will match the CAS number regardless of hyphens
      // This works by making each hyphen optional in the pattern
      const casRegexPattern = queryWithoutHyphens.split('').join('-?');

      console.log('API Suggestions: Detected potential CAS number search');
      console.log('API Suggestions: Original query:', query);
      console.log('API Suggestions: Query without hyphens:', queryWithoutHyphens);
      console.log('API Suggestions: CAS regex pattern:', casRegexPattern);

      // Use the special regex pattern for CAS numbers
      searchQuery.$or.push({ casNumber: { $regex: casRegexPattern, $options: 'i' } });
    } else {
      // For non-CAS number queries (no digits), use standard regex search
      searchQuery.$or.push({ casNumber: { $regex: query, $options: 'i' } });
    }

    console.log('API Suggestions: Final search query:', JSON.stringify(searchQuery));

    // Find chemicals matching the search criteria
    const chemicals = await Chemical.find(searchQuery)
      .select('name casNumber')
      .limit(10)
      .lean();

    console.log(`API Suggestions: Found ${chemicals.length} suggestions`);

    // Format the response
    const suggestions = chemicals.map(chemical => ({
      name: chemical.name,
      casNumber: chemical.casNumber
    }));

    return NextResponse.json({
      success: true,
      data: suggestions
    });
  } catch (error) {
    console.error('API Suggestions: Error getting suggestions:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error
      ? error.message
      : 'Unknown error occurred';

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get chemical suggestions',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

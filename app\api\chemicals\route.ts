import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import Chemical from '@/models/Chemical';
import Regulation from '@/models/Regulation';

// Add runtime directive for Node.js
export const runtime = 'nodejs';

// GET handler to fetch all chemicals
export async function GET(request: NextRequest) {
  try {
    console.log("API: Attempting to connect to database");
    // Connect to the database
    await dbConnect();
    console.log("API: Successfully connected to database");

    // Get query parameters for filtering
    const searchParams = request.nextUrl.searchParams;
    const searchQuery = searchParams.get('query');

    let query = {};
    if (searchQuery) {
      query = {
        $or: [
          { chemical_name: { $regex: searchQuery, $options: 'i' } },
          { cas_number: { $regex: searchQuery, $options: 'i' } },
          { alternative_names: { $in: [new RegExp(searchQuery, 'i')] } }
        ]
      };
    }

    console.log("API: Fetching chemicals with query:", JSON.stringify(query));

    // Fetch chemicals with the new embedded regulations schema
    const chemicals = await Chemical.find(query).lean();

    console.log(`API: Retrieved ${chemicals.length} chemicals`);

    // Log the first chemical's regulations for debugging
    if (chemicals.length > 0 && chemicals[0].regulations) {
      console.log(`API: First chemical has ${chemicals[0].regulations.length} regulations`);
    }

    // Return the data as JSON
    return NextResponse.json({ success: true, data: chemicals });
  } catch (error) {
    // Log server error and return JSON error response
    console.error('API Error fetching chemicals:', error);

    // Return a proper JSON error response
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : "An unknown error occurred",
        timestamp: new Date().toISOString(),
        path: '/api/chemicals'
      },
      { status: 500 }
    );
  }
}

// POST handler to add a new chemical
export async function POST(request: NextRequest) {
  await dbConnect();
  try {
    const body = await request.json();
    const { chemical_name, cas_number, alternative_names, ec_number, ...additionalFields } = body;

    // Validate required fields
    if (!chemical_name || !cas_number) {
      return NextResponse.json(
        { success: false, error: 'Chemical name and CAS number are required' },
        { status: 400 }
      );
    }

    // Check if chemical with CAS number already exists
    const existingChemical = await Chemical.findOne({ cas_number });
    if (existingChemical) {
      return NextResponse.json(
        { success: false, error: 'Chemical with this CAS number already exists' },
        { status: 409 }
      );
    }

    // Prepare additional data - capture ALL extra fields
    const additional_data: Record<string, any> = {};
    Object.keys(additionalFields).forEach(key => {
      // Skip empty values but keep zeros and false values
      if (additionalFields[key] !== null && additionalFields[key] !== undefined && additionalFields[key] !== '') {
        additional_data[key] = additionalFields[key];
      }
    });

    console.log('Creating chemical with additional data:', {
      chemical_name,
      cas_number,
      additional_fields_count: Object.keys(additional_data).length,
      additional_fields: Object.keys(additional_data)
    });

    // Create new chemical with ALL provided data
    const newChemical = await Chemical.create({
      chemical_name,
      cas_number,
      alternative_names: alternative_names || [],
      ec_number: ec_number || undefined,
      additional_data: Object.keys(additional_data).length > 0 ? additional_data : undefined,
      entry_method: 'manual',
      regulations: [],
      source_files: []
    });

    return NextResponse.json({
      success: true,
      data: newChemical,
      message: `Chemical added successfully with ${Object.keys(additional_data).length} additional fields captured`
    });
  } catch (error) {
    console.error("API Error adding chemical:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return NextResponse.json({ success: false, error: errorMessage }, { status: 400 });
  }
}

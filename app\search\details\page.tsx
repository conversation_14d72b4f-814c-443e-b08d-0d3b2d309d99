"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { ArrowLeft, FileText, AlertTriangle, Loader2, ExternalLink, Sparkles, Info } from "lucide-react"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import mongoose from "mongoose"

// Define types for our data
interface Regulation {
  _id: string;
  name: string;
  shortName?: string;
  country?: string;
  link?: string;
}

interface ChemicalRegulation {
  id: string;
  regulation: Regulation;
  smlValue?: string;
  smlUnit?: string;
  notes?: string;
  restrictions?: string;
}

interface AISummary {
  regulationId: string;
  regulationName: string;
  summary: string;
  keyPoints: string[];
  lastUpdated: string;
  confidence: number; // 0-1 value representing AI confidence
}

interface Chemical {
  _id: string;
  chemical_name: string;
  cas_number: string;
  alternative_names?: string[];
  ec_number?: string;
  regulations?: any[];
  source_files?: string[];
  additionalInfo?: Record<string, string>;
  aiSummaries?: AISummary[]; // AI-generated summaries
  created_at: string;
  updated_at: string;
}

interface RecentSearch {
  id: string;
  name: string;
  casNumber: string;
}

export default function ChemicalDetailsPage() {
  const searchParams = useSearchParams()
  const chemicalId = searchParams.get("id")
  const [chemical, setChemical] = useState<Chemical | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [recentSearches, setRecentSearches] = useState<RecentSearch[]>([])
  const [aiSummaries, setAiSummaries] = useState<AISummary[]>([])
  const [aiLoading, setAiLoading] = useState(false)
  const [aiError, setAiError] = useState<string | null>(null)

  // Helper function to validate MongoDB ObjectId
  const isValidObjectId = (id: string): boolean => {
    return mongoose.Types.ObjectId.isValid(id);
  }

  // Function to fetch AI summaries
  const fetchAiSummaries = async (id: string) => {
    try {
      setAiLoading(true)
      setAiError(null)

      // In a real implementation, this would call an actual AI service
      // For now, we'll use mock data
      const response = await fetch(`/api/ai-summary/${id}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Failed to fetch AI summaries')
      }

      setAiSummaries(result.data)
    } catch (err: unknown) {
      console.error('Error fetching AI summaries:', err)
      setAiError(err instanceof Error ? err.message : 'Failed to fetch AI summaries')
    } finally {
      setAiLoading(false)
    }
  }

  // Fetch chemical details
  useEffect(() => {
    if (!chemicalId) return

    const fetchChemicalDetails = async () => {
      try {
        setLoading(true)
        setError(null)

        // Validate the chemical ID format before making the API request
        if (!isValidObjectId(chemicalId)) {
          throw new Error('Invalid chemical ID format')
        }

        const response = await fetch(`/api/chemicals/${chemicalId}`)
        const result = await response.json()

        if (!response.ok) {
          throw new Error(result.error || 'Failed to fetch chemical details')
        }

        const chemicalData = result.data
        setChemical(chemicalData)

        // Fetch AI summaries after getting chemical details
        fetchAiSummaries(chemicalId)

        // Get recent searches from session storage and update with current chemical
        try {
          let recentSearchList = []
          const storedSearches = sessionStorage.getItem('recentChemicalSearches')

          if (storedSearches) {
            recentSearchList = JSON.parse(storedSearches)

            // Remove this chemical if it already exists in the list
            recentSearchList = recentSearchList.filter((item: RecentSearch) => item.id !== chemicalData._id)
          }

          // Add current chemical to the beginning of the list
          recentSearchList.unshift({
            id: chemicalData._id,
            name: chemicalData.chemical_name,
            casNumber: chemicalData.cas_number
          })

          // Limit to 5 recent searches
          if (recentSearchList.length > 5) {
            recentSearchList = recentSearchList.slice(0, 5)
          }

          // Save back to session storage
          sessionStorage.setItem('recentChemicalSearches', JSON.stringify(recentSearchList))

          // Update state
          setRecentSearches(recentSearchList)
        } catch (storageError) {
          console.error('Error accessing session storage:', storageError)
        }
      } catch (err: unknown) {
        console.error('Error fetching chemical details:', err)
        setError(err instanceof Error ? err.message : 'Failed to fetch chemical details')
      } finally {
        setLoading(false)
      }
    }

    fetchChemicalDetails()
  }, [chemicalId])

  if (!chemicalId) {
    return (
      <div className="container py-12">
        <Card>
          <CardHeader>
            <CardTitle>Chemical Not Found</CardTitle>
            <CardDescription>No chemical ID was provided</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/search">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Search
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="container py-12">
        <div className="flex flex-col items-center justify-center py-24">
          <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
          <p className="text-lg text-muted-foreground">Loading chemical details...</p>
        </div>
      </div>
    )
  }

  if (error || !chemical) {
    return (
      <div className="container py-12">
        <Card>
          <CardHeader>
            <CardTitle>Error Loading Chemical</CardTitle>
            <CardDescription>{error || 'Chemical not found'}</CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild>
              <Link href="/search">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Search
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }



  return (
    <div className="container py-8">
      {/* Header with navigation */}
      <div className="mb-8">
        <Button asChild variant="outline" className="mb-6">
          <Link href="/search">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Search
          </Link>
        </Button>

        {/* Hero Section */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-8 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
            <div className="flex-1">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">{chemical.chemical_name}</h1>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-600">CAS Number:</span>
                  <span className="text-sm font-mono bg-white px-2 py-1 rounded border">{chemical.cas_number}</span>
                </div>
                {chemical.ec_number && (
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-gray-600">EC Number:</span>
                    <span className="text-sm font-mono bg-white px-2 py-1 rounded border">{chemical.ec_number}</span>
                  </div>
                )}
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                  <span className="text-sm font-medium text-gray-600">Regulations:</span>
                  <Badge variant="secondary" className="bg-purple-100">
                    {chemical.regulations?.length || 0} found
                  </Badge>
                </div>
              </div>
              {chemical.alternative_names && chemical.alternative_names.length > 0 && (
                <div className="mt-4 p-4 bg-white rounded-lg border border-gray-200">
                  <h3 className="text-sm font-medium text-gray-700 mb-2">Alternative Names:</h3>
                  <div className="flex flex-wrap gap-2">
                    {chemical.alternative_names.map((name, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {name}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>


          </div>
        </div>
      </div>

      <div className="grid gap-8 lg:grid-cols-4">
        <div className="lg:col-span-3 space-y-8">

          <Tabs defaultValue="regulations" className="space-y-4">
            <TabsList className="inline-flex h-auto">
              <TabsTrigger value="regulations" className="h-10">Regulations</TabsTrigger>
              <TabsTrigger value="ai-analysis" className="h-10">AI Analysis</TabsTrigger>
              <TabsTrigger value="details" className="h-10">Chemical Details</TabsTrigger>
              {chemical.additional_data && Object.keys(chemical.additional_data).length > 0 && (
                <TabsTrigger value="additional-data" className="h-10">Additional Data</TabsTrigger>
              )}
              {chemical.additionalInfo && Object.keys(chemical.additionalInfo).length > 0 && (
                <TabsTrigger value="additional" className="h-10">Additional Information</TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="regulations">
              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-900">Related Regulations</h2>
                    <p className="text-gray-600 mt-1">
                      {chemical.regulations?.length || 0} regulation{(chemical.regulations?.length || 0) !== 1 ? 's' : ''} found for {chemical.chemical_name}
                    </p>
                  </div>
                  {chemical.regulations && chemical.regulations.length > 0 && (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                      {chemical.regulations.length} Total
                    </Badge>
                  )}
                </div>

                {chemical.regulations && chemical.regulations.length > 0 ? (
                  <div className="space-y-4">
                    {chemical.regulations.map((regulation: any, index: number) => (
                      <Card key={index} className="border-l-4 border-l-blue-500 hover:shadow-md transition-shadow">
                        <CardContent className="p-6">
                          <div className="grid gap-6 lg:grid-cols-3">
                            {/* Main Regulation Info */}
                            <div className="lg:col-span-2 space-y-4">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                                    {regulation.regulation_name}
                                  </h3>
                                  <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                                    <span className="flex items-center">
                                      <div className="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
                                      {regulation.country} - {regulation.region}
                                    </span>
                                    <Badge
                                      variant="secondary"
                                      className={
                                        regulation.list_type === 'positive' ? 'bg-green-100 text-green-800' :
                                        regulation.list_type === 'negative' ? 'bg-red-100 text-red-800' :
                                        regulation.list_type === 'mixed' ? 'bg-yellow-100 text-yellow-800' :
                                        'bg-gray-100 text-gray-800'
                                      }
                                    >
                                      {regulation.list_type || 'unknown'} list
                                    </Badge>
                                  </div>
                                </div>
                                <Button asChild variant="outline" size="sm">
                                  <Link href={`/regulations/details?id=${regulation.regulation_id}`}>
                                    <FileText className="h-4 w-4 mr-1" />
                                    View Details
                                  </Link>
                                </Button>
                              </div>

                              {/* Limits Section */}
                              {regulation.limits && (
                                <div className="bg-blue-50 rounded-lg p-4">
                                  <h4 className="text-sm font-medium text-blue-900 mb-2">Migration Limits</h4>
                                  <div className="flex flex-wrap gap-2">
                                    {regulation.limits.sml_value && (
                                      <div className="bg-white px-3 py-2 rounded border border-blue-200">
                                        <span className="text-xs text-blue-600 font-medium">SML:</span>
                                        <span className="text-sm font-mono ml-1">
                                          {regulation.limits.sml_value} {regulation.limits.sml_unit || 'mg/kg'}
                                        </span>
                                      </div>
                                    )}
                                    {regulation.limits.max_level_percent && (
                                      <div className="bg-white px-3 py-2 rounded border border-purple-200">
                                        <span className="text-xs text-purple-600 font-medium">Max Level:</span>
                                        <span className="text-sm font-mono ml-1">{regulation.limits.max_level_percent}%</span>
                                      </div>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>

                            {/* Notes and Additional Info */}
                            <div className="space-y-4">
                              {regulation.notes && (
                                <div className="bg-gray-50 rounded-lg p-4">
                                  <h4 className="text-sm font-medium text-gray-700 mb-2">Notes</h4>
                                  <p className="text-sm text-gray-600">{regulation.notes}</p>
                                </div>
                              )}

                              {regulation.restrictions && (
                                <div className="bg-orange-50 rounded-lg p-4">
                                  <h4 className="text-sm font-medium text-orange-700 mb-2">Restrictions</h4>
                                  <p className="text-sm text-orange-600">{regulation.restrictions}</p>
                                </div>
                              )}

                              {regulation.specific_data && Object.keys(regulation.specific_data).length > 0 && (
                                <div className="bg-indigo-50 rounded-lg p-4">
                                  <h4 className="text-sm font-medium text-indigo-700 mb-2">Additional Information</h4>
                                  <div className="space-y-1">
                                    {Object.entries(regulation.specific_data).map(([key, value]: [string, any]) => {
                                      if (key === 'additionalInfo' && typeof value === 'object' && value !== null) {
                                        return Object.entries(value).map(([nestedKey, nestedValue]: [string, any]) => (
                                          <div key={`${key}-${nestedKey}`} className="text-xs">
                                            <span className="font-medium text-indigo-600">{nestedKey}:</span>{' '}
                                            <span className="text-indigo-800">
                                              {typeof nestedValue === 'object' ? JSON.stringify(nestedValue) : String(nestedValue)}
                                            </span>
                                          </div>
                                        ));
                                      } else {
                                        return (
                                          <div key={key} className="text-xs">
                                            <span className="font-medium text-indigo-600">{key}:</span>{' '}
                                            <span className="text-indigo-800">
                                              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                            </span>
                                          </div>
                                        );
                                      }
                                    })}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No regulations found for this chemical</p>
                  </div>
                )}
              </div>
            </TabsContent>

            <TabsContent value="ai-analysis">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Sparkles className="h-5 w-5 mr-2 text-yellow-500" />
                    AI Analysis
                  </CardTitle>
                  <CardDescription>
                    AI-generated summaries and insights about {chemical.chemical_name} in various regulations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {aiLoading ? (
                    <div className="flex flex-col items-center justify-center py-8">
                      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
                      <p className="text-muted-foreground">Analyzing regulations...</p>
                    </div>
                  ) : aiError ? (
                    <div className="text-center py-8">
                      <AlertTriangle className="h-8 w-8 text-yellow-500 mx-auto mb-4" />
                      <p className="text-muted-foreground">{aiError}</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-4"
                        onClick={() => fetchAiSummaries(chemicalId!)}
                      >
                        Try Again
                      </Button>
                    </div>
                  ) : aiSummaries.length > 0 ? (
                    <Accordion type="single" collapsible className="w-full">
                      {aiSummaries.map((summary, index) => (
                        <AccordionItem key={index} value={`item-${index}`}>
                          <AccordionTrigger className="hover:no-underline">
                            <div className="flex items-center justify-between w-full pr-4">
                              <div className="flex items-center">
                                <Info className="h-4 w-4 mr-2 text-primary" />
                                <span>
                                  {summary.regulationName}
                                  {summary.confidence >= 0.8 && (
                                    <Badge variant="secondary" className="ml-2 bg-green-100">High Confidence</Badge>
                                  )}
                                </span>
                              </div>
                            </div>
                          </AccordionTrigger>
                          <AccordionContent>
                            <div className="space-y-4 pt-2">
                              <div className="text-sm text-muted-foreground">
                                Last updated: {new Date(summary.lastUpdated).toLocaleDateString()}
                              </div>
                              <div className="text-sm">
                                {summary.summary}
                              </div>
                              {summary.keyPoints.length > 0 && (
                                <div className="mt-4">
                                  <h4 className="text-sm font-medium mb-2">Key Points:</h4>
                                  <ul className="list-disc pl-5 space-y-1">
                                    {summary.keyPoints.map((point, i) => (
                                      <li key={i} className="text-sm">{point}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </AccordionContent>
                        </AccordionItem>
                      ))}
                    </Accordion>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">No AI analysis available for this chemical</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="details">
              <Card>
                <CardHeader>
                  <CardTitle>Chemical Details</CardTitle>
                  <CardDescription>Detailed information about {chemical.chemical_name}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Chemical Name</h3>
                        <p className="text-lg">{chemical.chemical_name}</p>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">CAS Number</h3>
                        <p className="text-lg">{chemical.cas_number}</p>
                      </div>
                      {chemical.ec_number && (
                        <div>
                          <h3 className="text-sm font-medium text-muted-foreground mb-1">EC Number</h3>
                          <p className="text-lg">{chemical.ec_number}</p>
                        </div>
                      )}
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Regulations Count</h3>
                        <p className="text-lg">{chemical.regulations?.length || 0}</p>
                      </div>
                    </div>

                    {chemical.alternative_names && chemical.alternative_names.length > 0 && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Alternative Names</h3>
                        <p>{chemical.alternative_names.join(', ')}</p>
                      </div>
                    )}

                    {chemical.source_files && chemical.source_files.length > 0 && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Source Files</h3>
                        <p>{chemical.source_files.join(', ')}</p>
                      </div>
                    )}

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Created At</h3>
                      <p>{new Date(chemical.created_at).toLocaleDateString()}</p>
                    </div>

                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Last Updated</h3>
                      <p>{new Date(chemical.updated_at).toLocaleDateString()}</p>
                    </div>

                    {chemical.entry_method && (
                      <div>
                        <h3 className="text-sm font-medium text-muted-foreground mb-1">Entry Method</h3>
                        <Badge variant="outline" className="capitalize">{chemical.entry_method}</Badge>
                      </div>
                    )}

                    {chemical.additional_data && Object.keys(chemical.additional_data).length > 0 && (
                      <div className="col-span-full">
                        <h3 className="text-sm font-medium text-muted-foreground mb-3">Additional Data Captured</h3>
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3 max-h-64 overflow-y-auto">
                            {Object.entries(chemical.additional_data).map(([key, value]: [string, any]) => (
                              <div key={key} className="bg-white rounded border p-3">
                                <div className="text-xs font-medium text-gray-600 mb-1">{key}</div>
                                <div className="text-sm text-gray-900 break-words">
                                  {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value || 'N/A')}
                                </div>
                              </div>
                            ))}
                          </div>
                          <div className="mt-3 text-xs text-gray-500">
                            {Object.keys(chemical.additional_data).length} additional fields captured
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {chemical.additional_data && Object.keys(chemical.additional_data).length > 0 && (
              <TabsContent value="additional-data">
                <Card>
                  <CardHeader>
                    <CardTitle>Additional Data Captured</CardTitle>
                    <CardDescription>All extra fields captured when this chemical was added ({chemical.entry_method})</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-6">
                      {/* Summary Stats */}
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-blue-50 rounded-lg p-4">
                          <div className="text-2xl font-bold text-blue-600">{Object.keys(chemical.additional_data).length}</div>
                          <div className="text-sm text-blue-600">Total Fields</div>
                        </div>
                        <div className="bg-green-50 rounded-lg p-4">
                          <div className="text-2xl font-bold text-green-600">{chemical.entry_method}</div>
                          <div className="text-sm text-green-600">Entry Method</div>
                        </div>
                        <div className="bg-purple-50 rounded-lg p-4">
                          <div className="text-2xl font-bold text-purple-600">
                            {new Date(chemical.created_at).toLocaleDateString()}
                          </div>
                          <div className="text-sm text-purple-600">Date Added</div>
                        </div>
                      </div>

                      {/* All Additional Data */}
                      <div>
                        <h3 className="text-lg font-semibold mb-4">All Captured Fields</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {Object.entries(chemical.additional_data).map(([key, value]: [string, any]) => (
                            <div key={key} className="bg-gray-50 rounded-lg p-4 border">
                              <div className="text-sm font-medium text-gray-700 mb-2">{key}</div>
                              <div className="text-sm text-gray-900 break-words">
                                {typeof value === 'object' ? (
                                  <pre className="text-xs bg-white p-2 rounded border overflow-auto max-h-32">
                                    {JSON.stringify(value, null, 2)}
                                  </pre>
                                ) : (
                                  <div className="bg-white p-2 rounded border">
                                    {String(value || 'N/A')}
                                  </div>
                                )}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}

            {chemical.additionalInfo && Object.keys(chemical.additionalInfo).length > 0 && (
              <TabsContent value="additional">
                <Card>
                  <CardHeader>
                    <CardTitle>Additional Information</CardTitle>
                    <CardDescription>Other data related to {chemical.chemical_name}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {Object.entries(chemical.additionalInfo).map(([key, value]: [string, string]) => (
                        <div key={key}>
                          <h3 className="text-sm font-medium text-muted-foreground mb-1">{key}</h3>
                          <p>{value}</p>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            )}
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="lg:col-span-1 space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Overview</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{chemical.regulations?.length || 0}</div>
                  <div className="text-xs text-blue-600 font-medium">Regulations</div>
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {chemical.regulations?.filter((r: any) => r.list_type === 'positive').length || 0}
                  </div>
                  <div className="text-xs text-green-600 font-medium">Positive Lists</div>
                </div>
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">
                    {chemical.regulations?.filter((r: any) => r.list_type === 'negative').length || 0}
                  </div>
                  <div className="text-xs text-red-600 font-medium">Negative Lists</div>
                </div>
                <div className="text-center p-3 bg-purple-50 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600">
                    {chemical.regulations?.filter((r: any) => r.limits?.sml_value).length || 0}
                  </div>
                  <div className="text-xs text-purple-600 font-medium">With Limits</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Searches */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Recent Searches</CardTitle>
              <CardDescription>Your recent chemical searches</CardDescription>
            </CardHeader>
            <CardContent>
              {recentSearches.length > 0 ? (
                <div className="space-y-3">
                  {recentSearches.slice(0, 5).map((result: RecentSearch) => (
                    <Link key={result.id} href={`/search/details?id=${result.id}`} className="block">
                      <div
                        className={`p-3 rounded-lg border hover:bg-gray-50 transition-colors ${
                          result.id === chemical._id ? "bg-blue-50 border-blue-200" : "border-gray-200"
                        }`}
                      >
                        <div className="font-medium text-sm">{result.name}</div>
                        <div className="text-xs text-gray-500 mt-1">CAS: {result.casNumber}</div>
                      </div>
                    </Link>
                  ))}
                  {recentSearches.length > 5 && (
                    <div className="text-center">
                      <Button asChild variant="ghost" size="sm">
                        <Link href="/search">
                          View All Searches
                        </Link>
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-sm text-gray-500">No recent searches found</p>
              )}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button asChild variant="outline" size="sm" className="w-full justify-start">
                <Link href="/search">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  New Search
                </Link>
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <ExternalLink className="h-4 w-4 mr-2" />
                Share Chemical
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}


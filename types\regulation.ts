// Type definitions for regulations and related entities

export interface UpdateHistoryType {
  date: Date;
  description: string;
}

export interface RegulationType {
  _id: string;
  name: string;
  shortName?: string;
  country: string;
  region: string;
  description?: string;
  link?: string;
  lastUpdated?: Date;
  createdAt?: Date;
  updatedAt?: Date;
  featured: boolean;
  categories: string[];
  list_type: 'positive' | 'negative' | 'mixed' | 'unknown';
  updateDetails?: string;
  updateHistory?: UpdateHistoryType[];
  fileId?: string;
  fileName?: string;
  fileSize?: number;
  fileType?: string;
  chemicals?: any[]; // Array of chemicals associated with this regulation
}

export interface ChemicalRegulationLimits {
  sml_value?: string;
  sml_unit?: string;
  max_level_percent?: number;
}

export interface ChemicalRegulationSourceInfo {
  file_name: string;
  file_type: string;
  imported_date: Date;
  row_number?: number;
  original_data?: Record<string, any>;
}

export interface ChemicalRegulationType {
  regulation_id: string;
  regulation_name: string;
  regulation_type: string;
  list_type: 'positive' | 'negative' | 'mixed' | 'unknown';
  country: string;
  region: string;
  limits?: ChemicalRegulationLimits;
  specific_data?: Record<string, any>;
  source_info: ChemicalRegulationSourceInfo;
  notes?: string;
  restrictions?: string;
  added_date: Date;
  updated_date: Date;
}

export interface ChemicalType {
  _id: string;
  chemical_name: string;
  cas_number: string;
  alternative_names: string[];
  ec_number?: string;
  regulations: ChemicalRegulationType[];
  source_files: string[];
  created_at: Date;
  updated_at: Date;
}

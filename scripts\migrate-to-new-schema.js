const mongoose = require('mongoose');

// Connection string - update this to match your MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/compliance-track';

// Define the old Chemical schema for migration
const OldChemicalSchema = new mongoose.Schema({
  name: String,
  casNumber: String,
  status: String,
  riskLevel: String,
  riskDescription: String,
  chemicalRegulations: [{ type: mongoose.Schema.Types.ObjectId, ref: 'ChemicalRegulationV2' }],
  uploadResults: [{ type: mongoose.Schema.Types.ObjectId, ref: 'UploadResult' }],
  calculationResults: [{ type: mongoose.Schema.Types.ObjectId, ref: 'CalculationResult' }],
}, { timestamps: true });

// Define the ChemicalRegulationV2 schema for migration
const ChemicalRegulationV2Schema = new mongoose.Schema({
  chemical: { type: mongoose.Schema.Types.ObjectId, ref: 'Chemical' },
  regulation: { type: mongoose.Schema.Types.ObjectId, ref: 'Regulation' },
  smlValue: String,
  smlUnit: String,
  notes: String,
  restrictions: String,
  additionalInfo: mongoose.Schema.Types.Mixed,
}, { timestamps: true });

// Define the Regulation schema
const RegulationSchema = new mongoose.Schema({
  name: String,
  shortName: String,
  country: String,
  region: String,
  description: String,
  link: String,
  lastUpdated: Date,
  featured: Boolean,
  categories: [String],
  list_type: {
    type: String,
    enum: ['positive', 'negative', 'mixed', 'unknown'],
    default: 'unknown'
  },
  updateDetails: String,
  updateHistory: [{
    date: { type: Date, default: Date.now },
    description: { type: String, required: true }
  }],
  fileId: String,
  fileName: String,
  fileSize: Number,
  fileType: String,
}, { timestamps: true });

// Define the new Chemical schema
const NewChemicalSchema = new mongoose.Schema({
  chemical_name: { type: String, required: true },
  cas_number: { type: String, required: true, unique: true },
  alternative_names: [String],
  ec_number: String,
  regulations: [{
    regulation_id: { type: mongoose.Schema.Types.ObjectId, required: true },
    regulation_name: { type: String, required: true },
    regulation_type: { type: String, required: true },
    list_type: { 
      type: String, 
      enum: ['positive', 'negative', 'mixed', 'unknown'], 
      required: true 
    },
    country: { type: String, required: true },
    region: { type: String, required: true },
    limits: {
      sml_value: String,
      sml_unit: String,
      max_level_percent: Number
    },
    specific_data: mongoose.Schema.Types.Mixed,
    source_info: {
      file_name: { type: String, required: true },
      file_type: { type: String, required: true },
      imported_date: { type: Date, required: true },
      row_number: Number,
      original_data: mongoose.Schema.Types.Mixed
    },
    notes: String,
    restrictions: String,
    added_date: { type: Date, default: Date.now },
    updated_date: { type: Date, default: Date.now }
  }],
  source_files: [String],
  uploadResults: [{ type: mongoose.Schema.Types.ObjectId, ref: 'UploadResult' }],
  calculationResults: [{ type: mongoose.Schema.Types.ObjectId, ref: 'CalculationResult' }],
}, { 
  timestamps: { createdAt: 'created_at', updatedAt: 'updated_at' }
});

async function migrateData() {
  try {
    console.log('🚀 Starting migration to new schema...');
    
    // Connect to MongoDB
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Create models
    const OldChemical = mongoose.model('OldChemical', OldChemicalSchema, 'chemicals');
    const ChemicalRegulationV2 = mongoose.model('ChemicalRegulationV2', ChemicalRegulationV2Schema);
    const Regulation = mongoose.model('Regulation', RegulationSchema);
    
    // Create a temporary collection for new chemicals
    const NewChemical = mongoose.model('NewChemical', NewChemicalSchema, 'chemicals_new');

    // Step 1: Get all old chemicals
    const oldChemicals = await OldChemical.find({}).lean();
    console.log(`📊 Found ${oldChemicals.length} chemicals to migrate`);

    // Step 2: Get all regulations for mapping
    const regulations = await Regulation.find({}).lean();
    const regulationMap = new Map(regulations.map(reg => [reg._id.toString(), reg]));
    console.log(`📊 Found ${regulations.length} regulations`);

    // Step 3: Get all chemical-regulation relationships
    const chemicalRegulations = await ChemicalRegulationV2.find({}).populate('regulation').lean();
    console.log(`📊 Found ${chemicalRegulations.length} chemical-regulation relationships`);

    // Group relationships by chemical
    const relationshipsByChemical = new Map();
    chemicalRegulations.forEach(rel => {
      const chemicalId = rel.chemical.toString();
      if (!relationshipsByChemical.has(chemicalId)) {
        relationshipsByChemical.set(chemicalId, []);
      }
      relationshipsByChemical.get(chemicalId).push(rel);
    });

    // Step 4: Migrate each chemical
    let migratedCount = 0;
    let errorCount = 0;

    for (const oldChem of oldChemicals) {
      try {
        const chemicalId = oldChem._id.toString();
        const relationships = relationshipsByChemical.get(chemicalId) || [];

        // Build regulations array for this chemical
        const newRegulations = [];
        
        for (const rel of relationships) {
          const regulation = rel.regulation;
          if (!regulation) continue;

          const newRegulation = {
            regulation_id: regulation._id,
            regulation_name: regulation.name,
            regulation_type: regulation.fileType || 'unknown',
            list_type: regulation.list_type || 'unknown',
            country: regulation.country,
            region: regulation.region,
            notes: rel.notes || '',
            restrictions: rel.restrictions || '',
            added_date: rel.createdAt || new Date(),
            updated_date: rel.updatedAt || new Date(),
            source_info: {
              file_name: regulation.fileName || 'unknown',
              file_type: regulation.fileType || 'unknown',
              imported_date: regulation.createdAt || new Date(),
              original_data: rel.additionalInfo || {}
            }
          };

          // Add limits if they exist
          if (rel.smlValue || rel.smlUnit) {
            newRegulation.limits = {
              sml_value: rel.smlValue,
              sml_unit: rel.smlUnit || 'mg/kg'
            };
          }

          // Add specific data from additionalInfo
          if (rel.additionalInfo && Object.keys(rel.additionalInfo).length > 0) {
            newRegulation.specific_data = rel.additionalInfo;
          }

          newRegulations.push(newRegulation);
        }

        // Create new chemical document
        const newChemical = {
          chemical_name: oldChem.name,
          cas_number: oldChem.casNumber,
          alternative_names: [],
          regulations: newRegulations,
          source_files: relationships.map(rel => rel.regulation?.fileName).filter(Boolean),
          uploadResults: oldChem.uploadResults || [],
          calculationResults: oldChem.calculationResults || [],
          created_at: oldChem.createdAt || new Date(),
          updated_at: oldChem.updatedAt || new Date()
        };

        // Save to new collection
        await NewChemical.create(newChemical);
        migratedCount++;

        if (migratedCount % 100 === 0) {
          console.log(`✅ Migrated ${migratedCount} chemicals...`);
        }

      } catch (error) {
        console.error(`❌ Error migrating chemical ${oldChem.name}:`, error.message);
        errorCount++;
      }
    }

    console.log(`\n🎉 Migration completed!`);
    console.log(`✅ Successfully migrated: ${migratedCount} chemicals`);
    console.log(`❌ Errors: ${errorCount} chemicals`);
    console.log(`\n📝 Next steps:`);
    console.log(`1. Verify the data in 'chemicals_new' collection`);
    console.log(`2. Rename 'chemicals' to 'chemicals_backup'`);
    console.log(`3. Rename 'chemicals_new' to 'chemicals'`);
    console.log(`4. Update your application to use the new schema`);
    console.log(`5. Remove old collections when confident`);

  } catch (error) {
    console.error('💥 Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

// Run the migration
if (require.main === module) {
  migrateData();
}

module.exports = { migrateData };

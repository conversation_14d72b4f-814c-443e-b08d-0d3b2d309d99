const { MongoClient } = require('mongodb');

async function checkChemicalFields() {
  const client = new MongoClient('mongodb://localhost:27017');
  
  try {
    await client.connect();
    console.log('Connected to MongoDB');
    
    const db = client.db('compliance-track');
    const collection = db.collection('chemicals');
    
    // Get a sample of chemicals to check their structure
    const samples = await collection.find({}).limit(5).toArray();
    
    console.log('\n=== Sample Chemical Documents ===');
    samples.forEach((chemical, index) => {
      console.log(`\nChemical ${index + 1}:`);
      console.log('ID:', chemical._id);
      console.log('Has chemical_name:', !!chemical.chemical_name);
      console.log('Has name (old):', !!chemical.name);
      console.log('Has cas_number:', !!chemical.cas_number);
      console.log('Has casNumber (old):', !!chemical.casNumber);
      console.log('Has regulations:', !!chemical.regulations);
      console.log('Has chemicalRegulations (old):', !!chemical.chemicalRegulations);
      
      if (chemical.chemical_name) {
        console.log('Chemical name:', chemical.chemical_name);
      }
      if (chemical.name) {
        console.log('Old name field:', chemical.name);
      }
      if (chemical.cas_number) {
        console.log('CAS number:', chemical.cas_number);
      }
      if (chemical.casNumber) {
        console.log('Old CAS field:', chemical.casNumber);
      }
    });
    
    // Count chemicals with old vs new field names
    const totalCount = await collection.countDocuments({});
    const newSchemaCount = await collection.countDocuments({ chemical_name: { $exists: true } });
    const oldSchemaCount = await collection.countDocuments({ name: { $exists: true } });
    
    console.log('\n=== Field Statistics ===');
    console.log('Total chemicals:', totalCount);
    console.log('Chemicals with new schema (chemical_name):', newSchemaCount);
    console.log('Chemicals with old schema (name):', oldSchemaCount);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await client.close();
  }
}

checkChemicalFields();
